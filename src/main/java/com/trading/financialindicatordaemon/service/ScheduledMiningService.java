package com.trading.financialindicatordaemon.service;

import com.trading.financialindicatordaemon.config.AppConfig;
import com.trading.financialindicatordaemon.entity.MiningSymbol;
import com.trading.financialindicatordaemon.mapper.MiningSymbolMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class ScheduledMiningService {

    private static final Logger logger = LoggerFactory.getLogger(ScheduledMiningService.class);

    private final MiningSymbolMapper miningSymbolMapper;
    private final DataMiningService dataMiningService;

    public ScheduledMiningService(MiningSymbolMapper miningSymbolMapper, DataMiningService dataMiningService) {
        this.miningSymbolMapper = miningSymbolMapper;
        this.dataMiningService = dataMiningService;
    }

    @Scheduled(cron = "0 */5 * * * *")
    public void mineActiveSymbols() {
        logger.info("Starting scheduled mining of active symbols");
        
        List<MiningSymbol> activeSymbols = miningSymbolMapper.findActiveSymbols();
        if (activeSymbols.isEmpty()) {
            logger.info("No active symbols found for mining");
            return;
        }

        List<String> symbols = activeSymbols.stream()
                .map(MiningSymbol::getSymbol)
                .toList();

        logger.info("Mining {} symbols: {}", symbols.size(), symbols);

        try {
            dataMiningService.mineSymbols(symbols, AppConfig.USD_CURRENCY_ID);
            
            symbols.forEach(symbol -> 
                miningSymbolMapper.updateLastMinedDate(symbol, LocalDate.now()));
            
            logger.info("Successfully completed mining for {} symbols", symbols.size());
        } catch (Exception e) {
            logger.error("Error during scheduled mining", e);
        }
    }
}
