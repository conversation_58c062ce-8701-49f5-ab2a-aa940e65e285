package com.trading.financialindicatordaemon.mapper;

import com.trading.financialindicatordaemon.entity.MiningSymbol;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface MiningSymbolMapper {

    List<MiningSymbol> findActiveSymbols();

    void updateLastMinedDate(@Param("symbol") String symbol, @Param("date") LocalDate date);

    void insert(MiningSymbol miningSymbol);

    void updateStatus(@Param("symbol") String symbol, @Param("status") String status);
}
