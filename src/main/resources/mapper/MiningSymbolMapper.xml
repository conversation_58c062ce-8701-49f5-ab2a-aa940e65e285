<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trading.financialindicatordaemon.mapper.MiningSymbolMapper">

    <select id="findActiveSymbols" resultType="com.trading.financialindicatordaemon.entity.MiningSymbol">
        SELECT id, symbol, status, last_mined_date, created_at, updated_at
        FROM crypto_data.mining_symbols
        WHERE status = 'ACTIVE'
        ORDER BY id
    </select>

    <update id="updateLastMinedDate">
        UPDATE crypto_data.mining_symbols
        SET last_mined_date = #{date}, updated_at = CURRENT_TIMESTAMP
        WHERE symbol = #{symbol}
    </update>

    <insert id="insert">
        INSERT INTO crypto_data.mining_symbols (symbol, status)
        VALUES (#{symbol}, #{status})
    </insert>

    <update id="updateStatus">
        UPDATE crypto_data.mining_symbols
        SET status = #{status}, updated_at = CURRENT_TIMESTAMP
        WHERE symbol = #{symbol}
    </update>

</mapper>
